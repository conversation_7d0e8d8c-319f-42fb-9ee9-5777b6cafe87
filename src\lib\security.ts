import { NextRequest, NextResponse } from 'next/server'

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number; requests: number[] }>()

interface RateLimitConfig {
  windowMs: number
  maxRequests: number
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  keyGenerator?: (request: NextRequest) => string
}

interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  retryAfter?: number
}

/**
 * Advanced rate limiting with sliding window
 */
export function createRateLimit(config: RateLimitConfig) {
  return (request: NextRequest): RateLimitResult => {
    const clientId = config.keyGenerator ? config.keyGenerator(request) : getClientIdentifier(request)
    const now = Date.now()

    // Clean up expired entries
    for (const [key, value] of rateLimitStore.entries()) {
      if (now > value.resetTime) {
        rateLimitStore.delete(key)
      }
    }

    const existing = rateLimitStore.get(clientId)

    if (!existing || now > existing.resetTime) {
      // New window
      rateLimitStore.set(clientId, {
        count: 1,
        resetTime: now + config.windowMs,
        requests: [now]
      })
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: now + config.windowMs
      }
    }

    // Remove old requests outside the window
    existing.requests = existing.requests.filter(time => time > now - config.windowMs)

    if (existing.requests.length >= config.maxRequests) {
      const oldestRequest = Math.min(...existing.requests)
      const retryAfter = Math.ceil((oldestRequest + config.windowMs - now) / 1000)

      return {
        allowed: false,
        remaining: 0,
        resetTime: existing.resetTime,
        retryAfter
      }
    }

    existing.requests.push(now)
    existing.count = existing.requests.length

    return {
      allowed: true,
      remaining: config.maxRequests - existing.count,
      resetTime: existing.resetTime
    }
  }
}

/**
 * Get client identifier for rate limiting
 */
function getClientIdentifier(request: NextRequest): string {
  // Try to get real IP from various headers
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')

  const ip = forwarded?.split(',')[0]?.trim() || realIp || cfConnectingIp || 'unknown'

  // Include user agent for additional uniqueness (but limit length)
  const userAgent = request.headers.get('user-agent')?.slice(0, 50) || 'unknown'

  return `${ip}:${userAgent}`
}

/**
 * Rate limit configurations for different endpoints
 */
export const rateLimits = {
  // General API rate limit
  api: createRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 500 // Increased from 100 to 500 for development
  }),

  // Scan operations (reasonable for dashboard usage)
  scan: createRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 2000 // Allow frequent dashboard refreshes
  }),

  // Authentication operations (reasonable for development)
  auth: createRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 500 // Increased from 5 to 50 for development
  }),

  // Health checks (more permissive)
  health: createRateLimit({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 600
  })
}

/**
 * Apply rate limiting to a response
 */
export function applyRateLimit(request: NextRequest, response: NextResponse, limitType: keyof typeof rateLimits): NextResponse {
  const rateLimit = rateLimits[limitType]
  const result = rateLimit(request)

  // Add rate limit headers
  response.headers.set('X-RateLimit-Limit', rateLimits[limitType].toString())
  response.headers.set('X-RateLimit-Remaining', result.remaining.toString())
  response.headers.set('X-RateLimit-Reset', new Date(result.resetTime).toISOString())

  if (!result.allowed) {
    response.headers.set('Retry-After', (result.retryAfter || 60).toString())
    return NextResponse.json({
      error: 'Rate limit exceeded',
      message: `Too many requests. Try again in ${result.retryAfter || 60} seconds.`,
      retryAfter: result.retryAfter || 60
    }, { status: 429 })
  }

  return response
}

// Security headers configuration
export const securityHeaders = {
  // Prevent clickjacking attacks
  'X-Frame-Options': 'DENY',
  
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // Control referrer information
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // XSS protection (legacy, but still useful)
  'X-XSS-Protection': '1; mode=block',
  
  // Strict Transport Security (HTTPS only)
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  
  // Content Security Policy
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self'",
    "frame-ancestors 'none'",
  ].join('; '),
  
  // Permissions Policy (formerly Feature Policy)
  'Permissions-Policy': [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
  ].join(', '),
}

// Apply security headers to response
export function addSecurityHeaders(response: NextResponse): NextResponse {
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  
  return response
}

// Input sanitization
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return ''
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .slice(0, 1000) // Limit length
}

// Email validation (additional to Zod)
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email) && email.length <= 254
}

// Password strength validation
export function validatePasswordStrength(password: string): {
  isValid: boolean
  score: number
  feedback: string[]
} {
  const feedback: string[] = []
  let score = 0
  
  if (password.length >= 8) score += 1
  else feedback.push('Password should be at least 8 characters long')
  
  if (/[a-z]/.test(password)) score += 1
  else feedback.push('Password should contain lowercase letters')
  
  if (/[A-Z]/.test(password)) score += 1
  else feedback.push('Password should contain uppercase letters')
  
  if (/\d/.test(password)) score += 1
  else feedback.push('Password should contain numbers')
  
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1
  else feedback.push('Password should contain special characters')
  
  if (password.length >= 12) score += 1
  
  return {
    isValid: score >= 4,
    score,
    feedback
  }
}

// Generate secure random string
export function generateSecureToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  
  return result
}

// Check for common passwords
const commonPasswords = [
  'password', '123456', '123456789', 'qwerty', 'abc123',
  'password123', 'admin', 'letmein', 'welcome', 'monkey'
]

export function isCommonPassword(password: string): boolean {
  return commonPasswords.includes(password.toLowerCase())
}

/**
 * URL validation and sanitization for scanning
 *
 * Enhanced to allow legitimate penetration testing and security research domains
 * while blocking actual internal/private domains and IP addresses.
 *
 * Key improvements:
 * - More specific pattern matching for blocked domains
 * - Whitelist of legitimate testing platforms
 * - Support for security research domains
 * - Flexible matching for educational and testing purposes
 */
export function validateScanUrl(url: string): {
  isValid: boolean
  sanitizedUrl?: string
  error?: string
} {
  try {
    console.log(`🔍 Validating URL: ${url}`)

    // Basic URL validation
    const parsed = new URL(url)

    // Only allow HTTP and HTTPS
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      return {
        isValid: false,
        error: 'Only HTTP and HTTPS protocols are allowed'
      }
    }

    // Block localhost and private IP ranges
    const hostname = parsed.hostname.toLowerCase()
    console.log(`🌐 Hostname: ${hostname}`)

    // Block localhost variations
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '::1') {
      console.log(`❌ Blocked: localhost variation`)
      return {
        isValid: false,
        error: 'Scanning localhost is not allowed'
      }
    }

    // Block private IP ranges
    if (isPrivateIP(hostname)) {
      console.log(`❌ Blocked: private IP address`)
      return {
        isValid: false,
        error: 'Scanning private IP addresses is not allowed'
      }
    }

    // Block common internal domains (more specific patterns)
    const blockedPatterns = [
      /^internal\./,           // internal.company.com
      /^intranet\./,          // intranet.company.com
      /^corp\./,              // corp.company.com
      /\.local$/,             // anything.local
      /\.lan$/,               // anything.lan
      /^admin\./,             // admin.company.com
      /^test\./,              // test.company.com (but not pentest-ground.com)
      /^dev\./,               // dev.company.com
      /^staging\./,           // staging.company.com
      /^localhost$/,          // exact localhost match
      /^127\./,               // 127.x.x.x
      /^10\./,                // 10.x.x.x
      /^172\.1[6-9]\./,       // 172.16-19.x.x
      /^172\.2[0-9]\./,       // 172.20-29.x.x
      /^172\.3[0-1]\./,       // 172.30-31.x.x
      /^192\.168\./,          // 192.168.x.x
    ]

    // Allow legitimate penetration testing and security research domains
    const allowedTestingDomains = [
      // Penetration Testing Platforms
      'pentest-ground.com',
      'testphp.vulnweb.com',
      'demo.testfire.net',
      'zero.webappsecurity.com',
      'dvwa.co.uk',
      'bwapp.hakhub.net',
      'mutillidae.sourceforge.net',
      'webgoat.owasp.org',

      // Security Training Platforms
      'hackthissite.org',
      'overthewire.org',
      'vulnhub.com',
      'tryhackme.com',
      'hackthebox.eu',
      'hackthebox.com',
      'picoctf.org',
      'ctftime.org',

      // Security Research & Tools
      'portswigger.net',
      'owasp.org',
      'scanme.nmap.org',
      'testssl.sh',
      'badssl.com',
      'ssllabs.com',
      'securityheaders.com',
      'observatory.mozilla.org',

      // API Testing Services
      'httpbin.org',
      'httpbingo.org',
      'postman-echo.com',
      'jsonplaceholder.typicode.com',
      'reqres.in',
      'mockapi.io',

      // Bug Bounty Platforms
      'bugcrowd.com',
      'hackerone.com',
      'intigriti.com',
      'synack.com',

      // Security Vendors (for testing)
      'rapid7.com',
      'tenable.com',
      'qualys.com',
      'veracode.com',
      'checkmarx.com',

      // Educational Institutions (common for security research)
      '.edu',
      '.ac.uk',
      '.edu.au',

      // Legitimate testing subdomains
      'test.example.com',
      'demo.example.com',
      'sandbox.example.com'
    ]

    // Check if it's an allowed testing domain (more flexible matching)
    const isAllowedTestingDomain = allowedTestingDomains.some(allowed => {
      // Exact match
      if (hostname === allowed) {
        console.log(`✅ Allowed: exact match with ${allowed}`)
        return true
      }

      // Subdomain match (e.g., api.pentest-ground.com)
      if (hostname.endsWith('.' + allowed)) {
        console.log(`✅ Allowed: subdomain match with ${allowed}`)
        return true
      }

      // TLD match for educational domains
      if (allowed.startsWith('.') && hostname.endsWith(allowed)) {
        console.log(`✅ Allowed: TLD match with ${allowed}`)
        return true
      }

      // Partial match for common testing patterns
      if (allowed.includes('example.com') && hostname.includes('example.com')) {
        console.log(`✅ Allowed: example.com pattern match`)
        return true
      }

      return false
    })

    // Additional check for legitimate domains that might contain blocked keywords
    const isLegitimateTestingDomain = (
      hostname.includes('pentest') ||
      hostname.includes('security') ||
      hostname.includes('vulnweb') ||
      hostname.includes('hackthebox') ||
      hostname.includes('tryhackme') ||
      hostname.includes('bugbounty') ||
      hostname.includes('ctf') ||
      hostname.includes('owasp') ||
      hostname.includes('demo') && !hostname.startsWith('demo.') // Allow demo in middle but not as subdomain
    )

    console.log(`🔍 Domain validation results:`)
    console.log(`   • Allowed testing domain: ${isAllowedTestingDomain}`)
    console.log(`   • Legitimate testing domain: ${isLegitimateTestingDomain}`)

    // Check if any blocked patterns match
    const matchedBlockedPattern = blockedPatterns.find(pattern => pattern.test(hostname))
    if (matchedBlockedPattern) {
      console.log(`⚠️  Matched blocked pattern: ${matchedBlockedPattern}`)
    }

    if (!isAllowedTestingDomain && !isLegitimateTestingDomain && blockedPatterns.some(pattern => pattern.test(hostname))) {
      console.log(`❌ Blocked: internal domain pattern detected`)
      return {
        isValid: false,
        error: 'Scanning internal domains is not allowed'
      }
    }

    // Sanitize URL
    const sanitizedUrl = `${parsed.protocol}//${parsed.host}${parsed.pathname}${parsed.search}`
    console.log(`✅ URL validation passed: ${sanitizedUrl}`)

    return {
      isValid: true,
      sanitizedUrl
    }
  } catch (error) {
    console.log(`❌ URL validation failed: Invalid URL format - ${error}`)
    return {
      isValid: false,
      error: 'Invalid URL format'
    }
  }
}

function isPrivateIP(hostname: string): boolean {
  // IPv4 private ranges - more comprehensive check
  const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/
  const match = hostname.match(ipv4Regex)

  if (match) {
    const [, a, b, c, d] = match.map(Number)

    // Validate IP address ranges (0-255)
    if (a > 255 || b > 255 || c > 255 || d > 255) return false

    // 10.0.0.0/8 (Class A private)
    if (a === 10) return true

    // **********/12 (Class B private)
    if (a === 172 && b >= 16 && b <= 31) return true

    // ***********/16 (Class C private)
    if (a === 192 && b === 168) return true

    // ***********/16 (link-local)
    if (a === 169 && b === 254) return true

    // *********/8 (loopback)
    if (a === 127) return true

    // 0.0.0.0/8 (this network)
    if (a === 0) return true

    // *********/4 (multicast)
    if (a >= 224 && a <= 239) return true

    // 240.0.0.0/4 (reserved)
    if (a >= 240) return true
  }

  // IPv6 private/local addresses
  if (hostname.includes(':')) {
    // IPv6 loopback
    if (hostname === '::1') return true

    // IPv6 link-local (fe80::/10)
    if (hostname.toLowerCase().startsWith('fe80:')) return true

    // IPv6 unique local (fc00::/7)
    if (hostname.toLowerCase().startsWith('fc') || hostname.toLowerCase().startsWith('fd')) return true

    // IPv6 site-local (deprecated, fec0::/10)
    if (hostname.toLowerCase().startsWith('fec')) return true
  }

  return false
}

// Rate limiting for scan operations
export function getScanRateLimit(userId: string): {
  maxScansPerHour: number
  maxConcurrentScans: number
} {
  // Increased limits for testing - can be enhanced based on user tiers
  return {
    maxScansPerHour: 100,  // Increased from 10 to 100 for testing
    maxConcurrentScans: 10  // Increased from 2 to 10 for testing
  }
}

// Validate scan parameters
export function validateScanParameters(params: any): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // Validate severity levels
  if (params.severity && Array.isArray(params.severity)) {
    const validSeverities = ['critical', 'high', 'medium', 'low', 'info', 'unknown']
    const invalidSeverities = params.severity.filter((s: string) => !validSeverities.includes(s))
    if (invalidSeverities.length > 0) {
      errors.push(`Invalid severity levels: ${invalidSeverities.join(', ')}`)
    }
  }

  // Validate tags
  if (params.tags && Array.isArray(params.tags)) {
    const invalidTags = params.tags.filter((tag: string) =>
      typeof tag !== 'string' || tag.length > 50 || !/^[a-zA-Z0-9-_]+$/.test(tag)
    )
    if (invalidTags.length > 0) {
      errors.push('Invalid tag format. Tags must be alphanumeric with hyphens and underscores only')
    }
  }

  // Validate template limits
  if (params.templates && Array.isArray(params.templates)) {
    if (params.templates.length > 100) {
      errors.push('Too many templates specified. Maximum 100 templates allowed')
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Extract and validate client IP
 */
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')

  // Get the first IP from x-forwarded-for (in case of multiple proxies)
  const ip = forwarded?.split(',')[0]?.trim() || realIp || cfConnectingIp || 'unknown'

  // Basic IP validation
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

  if (ip !== 'unknown' && !ipRegex.test(ip) && !ip.includes(':')) {
    console.warn(`⚠️ Invalid IP format detected: ${ip}`)
    return 'unknown'
  }

  return ip
}

/**
 * Log security events
 */
export function logSecurityEvent(event: string, details: Record<string, any>, request: NextRequest): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    clientIP: getClientIP(request),
    userAgent: request.headers.get('user-agent'),
    path: request.nextUrl.pathname,
    method: request.method,
    ...details
  }

  console.warn(`🔒 Security Event: ${event}`, logEntry)

  // In production, send to security monitoring system
  if (process.env.NODE_ENV === 'production') {
    // TODO: Send to security monitoring service
  }
}
