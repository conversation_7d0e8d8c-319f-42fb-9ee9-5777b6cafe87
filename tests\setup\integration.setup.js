const { PrismaClient } = require('@prisma/client')
const request = require('supertest')
const express = require('express')
const bcrypt = require('bcryptjs')

// Setup for integration tests
let prisma
let app, server, baseURL

// Increase timeout for integration tests
jest.setTimeout(30000)

beforeAll(async () => {
  // Mock environment variables for tests
  process.env.NODE_ENV = 'test'
  process.env.JWT_SECRET = 'test-jwt-secret'
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'mysql://root:rootroot@localhost:3306/ctb_scanner_test'

  // Initialize test database
  prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.TEST_DATABASE_URL || 'mysql://root:rootroot@localhost:3306/ctb_scanner_test'
      }
    }
  })

  // Connect to database
  await prisma.$connect()

  // Clean database before tests
  await cleanDatabase()

  // Setup Express app for testing (simpler than Next.js)
  app = express()
  app.use(express.json())
  app.use(require('cookie-parser')())

  // Import and setup API routes
  const createAuthRouter = require('../api-mocks/auth')
  const createScansRouter = require('../api-mocks/scans')

  app.use('/api/auth', createAuthRouter(prisma))
  app.use('/api/scans', createScansRouter(prisma))

  // Start server
  server = app.listen(0)
  const address = server.address()
  baseURL = `http://localhost:${address.port}`
})

beforeEach(async () => {
  // Clean up database before each test
  await cleanDatabase()
})

afterAll(async () => {
  // Clean up after all tests
  await cleanDatabase()
  await prisma.$disconnect()

  // Close server
  if (server) {
    server.close()
  }
})

// Helper function to make test requests
const testRequest = () => {
  return request(app)
}

// Helper function to create test user and get auth cookie
const createTestUser = async (userData = {}) => {
  const defaultUser = {
    firstName: 'Test',
    lastName: 'User',
    companyName: 'Test Company',
    country: 'US',
    email: `test-${Date.now()}@example.com`,
    password: 'testpassword123',
    confirmPassword: 'testpassword123',
    ...userData
  }

  // Create user
  const signupResponse = await request(app)
    .post('/api/auth/signup')
    .send(defaultUser)
    .expect(201)

  // Login to get auth cookie
  const loginResponse = await testRequest()
    .post('/api/auth/login')
    .send({
      email: defaultUser.email,
      password: defaultUser.password
    })

  if (loginResponse.status !== 200) {
    console.error('Login failed:', loginResponse.status, loginResponse.body)
    throw new Error(`Login failed with status ${loginResponse.status}`)
  }

  const authCookie = loginResponse.headers['set-cookie']
    ?.find(cookie => cookie.startsWith('token='))
    ?.split(';')[0] // Remove additional cookie attributes



  return {
    user: signupResponse.body.user,
    authCookie
  }
}

// Make helper functions globally available
global.testRequest = testRequest
global.createTestUser = createTestUser



async function cleanDatabase() {
  // Delete in correct order to respect foreign key constraints
  await prisma.vulnerability.deleteMany()
  await prisma.scan.deleteMany()
  await prisma.asset.deleteMany()
  await prisma.user.deleteMany()
}

// Export helper functions
module.exports = {
  testRequest,
  createTestUser
}
